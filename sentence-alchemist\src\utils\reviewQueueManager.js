/**
 * 复习队列管理系统
 * 管理复习队列、优先级排序、智能调度和复习提醒
 */

import { learningStateManager, LEARNING_STATE } from './learningStateManager.js';

// 复习优先级枚举
export const REVIEW_PRIORITY = {
  URGENT: 'urgent',       // 紧急复习（遗忘概率 > 0.8）
  HIGH: 'high',          // 高优先级（遗忘概率 > 0.6）
  MEDIUM: 'medium',      // 中等优先级（遗忘概率 > 0.4）
  LOW: 'low',           // 低优先级（遗忘概率 > 0.2）
  OPTIONAL: 'optional'   // 可选复习（遗忘概率 <= 0.2）
};

// 复习类型枚举
export const REVIEW_TYPE = {
  SCHEDULED: 'scheduled',     // 计划复习
  REINFORCEMENT: 'reinforcement', // 强化复习
  DIFFICULTY: 'difficulty',   // 困难句子复习
  RANDOM: 'random'           // 随机复习
};

/**
 * 复习队列管理器类
 */
export class ReviewQueueManager {
  constructor() {
    this.reviewQueues = {
      [REVIEW_PRIORITY.URGENT]: [],
      [REVIEW_PRIORITY.HIGH]: [],
      [REVIEW_PRIORITY.MEDIUM]: [],
      [REVIEW_PRIORITY.LOW]: [],
      [REVIEW_PRIORITY.OPTIONAL]: []
    };
    
    this.scheduledReviews = new Map(); // 计划复习时间表
    this.reviewHistory = new Map();    // 复习历史记录
    this.reminderSettings = {
      enabled: true,
      dailyGoal: 20,              // 每日复习目标
      reminderTimes: ['09:00', '18:00'], // 提醒时间
      maxDailyReviews: 50         // 每日最大复习数量
    };
  }

  /**
   * 更新复习队列
   * @param {Object[]} sentences - 句子数组
   */
  updateReviewQueues(sentences) {
    // 清空现有队列
    Object.keys(this.reviewQueues).forEach(priority => {
      this.reviewQueues[priority] = [];
    });

    // 获取需要复习的句子
    const reviewSentences = learningStateManager.getReviewSentences(sentences);
    
    // 按优先级分类
    reviewSentences.forEach(sentence => {
      const priority = this.calculateReviewPriority(sentence);
      const reviewItem = this.createReviewItem(sentence, REVIEW_TYPE.SCHEDULED);
      this.reviewQueues[priority].push(reviewItem);
    });

    // 添加困难句子到队列
    const difficultSentences = learningStateManager.getDifficultSentences(sentences);
    difficultSentences.forEach(sentence => {
      const reviewItem = this.createReviewItem(sentence, REVIEW_TYPE.DIFFICULTY);
      this.reviewQueues[REVIEW_PRIORITY.HIGH].push(reviewItem);
    });

    // 对每个队列按优先级分数排序
    Object.keys(this.reviewQueues).forEach(priority => {
      this.reviewQueues[priority].sort((a, b) => b.priorityScore - a.priorityScore);
    });
  }

  /**
   * 计算复习优先级
   * @param {Object} sentence - 句子对象
   * @returns {string} 优先级级别
   */
  calculateReviewPriority(sentence) {
    const learningData = sentence.learningData || {};
    const forgettingProb = learningStateManager.spacedRepetition.predictForgettingProbability(learningData);
    
    if (forgettingProb > 0.8) return REVIEW_PRIORITY.URGENT;
    if (forgettingProb > 0.6) return REVIEW_PRIORITY.HIGH;
    if (forgettingProb > 0.4) return REVIEW_PRIORITY.MEDIUM;
    if (forgettingProb > 0.2) return REVIEW_PRIORITY.LOW;
    return REVIEW_PRIORITY.OPTIONAL;
  }

  /**
   * 创建复习项目
   * @param {Object} sentence - 句子对象
   * @param {string} type - 复习类型
   * @returns {Object} 复习项目
   */
  createReviewItem(sentence, type) {
    const learningData = sentence.learningData || {};
    const forgettingProb = learningStateManager.spacedRepetition.predictForgettingProbability(learningData);
    const memoryStrength = learningStateManager.spacedRepetition.calculateMemoryStrength(learningData);
    const daysSinceReview = learningData.lastReviewed 
      ? (new Date() - new Date(learningData.lastReviewed)) / (1000 * 60 * 60 * 24)
      : 999;

    // 计算优先级分数
    let priorityScore = forgettingProb * 0.5 + Math.min(daysSinceReview / 30, 1) * 0.3;
    
    // 根据复习类型调整分数
    switch (type) {
      case REVIEW_TYPE.DIFFICULTY:
        priorityScore += 0.2; // 困难句子优先级更高
        break;
      case REVIEW_TYPE.REINFORCEMENT:
        priorityScore += 0.1; // 强化复习适度提升
        break;
    }

    return {
      sentence,
      type,
      priorityScore,
      forgettingProbability: forgettingProb,
      memoryStrength,
      daysSinceReview,
      addedToQueue: new Date(),
      estimatedReviewTime: this.estimateReviewTime(sentence)
    };
  }

  /**
   * 估算复习时间
   * @param {Object} sentence - 句子对象
   * @returns {number} 估算时间（秒）
   */
  estimateReviewTime(sentence) {
    const baseTime = {
      'beginner': 30,
      'intermediate': 45,
      'advanced': 60
    };
    
    const learningData = sentence.learningData || {};
    const accuracy = learningData.totalAttempts > 0 
      ? learningData.correctCount / learningData.totalAttempts 
      : 0;
    
    // 准确率低的句子需要更多时间
    const difficultyMultiplier = accuracy < 0.5 ? 1.5 : 1.0;
    
    return (baseTime[sentence.difficulty] || 45) * difficultyMultiplier;
  }

  /**
   * 获取下一批复习句子
   * @param {number} count - 需要的句子数量
   * @param {number} maxTime - 最大复习时间（秒）
   * @returns {Object[]} 复习句子数组
   */
  getNextReviewBatch(count = 10, maxTime = 600) {
    const batch = [];
    let totalTime = 0;
    
    // 按优先级顺序获取句子
    const priorities = [
      REVIEW_PRIORITY.URGENT,
      REVIEW_PRIORITY.HIGH,
      REVIEW_PRIORITY.MEDIUM,
      REVIEW_PRIORITY.LOW,
      REVIEW_PRIORITY.OPTIONAL
    ];
    
    for (const priority of priorities) {
      const queue = this.reviewQueues[priority];
      
      for (let i = 0; i < queue.length && batch.length < count; i++) {
        const reviewItem = queue[i];
        
        if (totalTime + reviewItem.estimatedReviewTime <= maxTime) {
          batch.push(reviewItem);
          totalTime += reviewItem.estimatedReviewTime;
          
          // 从队列中移除
          queue.splice(i, 1);
          i--; // 调整索引
        }
      }
      
      if (batch.length >= count) break;
    }
    
    return batch;
  }

  /**
   * 获取复习统计信息
   * @returns {Object} 复习统计
   */
  getReviewStats() {
    const stats = {
      totalPending: 0,
      byPriority: {},
      byType: {},
      estimatedTotalTime: 0,
      dailyProgress: this.getDailyProgress()
    };
    
    // 统计各优先级队列
    Object.keys(this.reviewQueues).forEach(priority => {
      const count = this.reviewQueues[priority].length;
      stats.byPriority[priority] = count;
      stats.totalPending += count;
      
      // 计算估算时间
      const time = this.reviewQueues[priority].reduce((sum, item) => sum + item.estimatedReviewTime, 0);
      stats.estimatedTotalTime += time;
    });
    
    // 统计各类型
    Object.keys(this.reviewQueues).forEach(priority => {
      this.reviewQueues[priority].forEach(item => {
        stats.byType[item.type] = (stats.byType[item.type] || 0) + 1;
      });
    });
    
    return stats;
  }

  /**
   * 获取每日进度
   * @returns {Object} 每日进度信息
   */
  getDailyProgress() {
    const today = new Date().toDateString();
    const todayReviews = Array.from(this.reviewHistory.values())
      .filter(review => new Date(review.completedAt).toDateString() === today);
    
    return {
      completed: todayReviews.length,
      goal: this.reminderSettings.dailyGoal,
      accuracy: todayReviews.length > 0 
        ? todayReviews.filter(r => r.isCorrect).length / todayReviews.length 
        : 0,
      timeSpent: todayReviews.reduce((sum, r) => sum + (r.responseTime || 0), 0)
    };
  }

  /**
   * 记录复习完成
   * @param {Object} reviewItem - 复习项目
   * @param {boolean} isCorrect - 是否答对
   * @param {number} responseTime - 响应时间
   */
  recordReviewCompletion(reviewItem, isCorrect, responseTime) {
    const reviewRecord = {
      sentenceId: reviewItem.sentence.id,
      type: reviewItem.type,
      isCorrect,
      responseTime,
      completedAt: new Date(),
      forgettingProbabilityBefore: reviewItem.forgettingProbability,
      memoryStrengthBefore: reviewItem.memoryStrength
    };
    
    this.reviewHistory.set(`${reviewItem.sentence.id}_${Date.now()}`, reviewRecord);
    
    // 清理旧记录（保留最近30天）
    this.cleanupOldRecords();
  }

  /**
   * 清理旧的复习记录
   */
  cleanupOldRecords() {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    for (const [key, record] of this.reviewHistory.entries()) {
      if (new Date(record.completedAt) < thirtyDaysAgo) {
        this.reviewHistory.delete(key);
      }
    }
  }

  /**
   * 检查是否需要发送复习提醒
   * @returns {Object} 提醒信息
   */
  checkReviewReminder() {
    if (!this.reminderSettings.enabled) {
      return { shouldRemind: false };
    }
    
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    const dailyProgress = this.getDailyProgress();
    const stats = this.getReviewStats();
    
    const shouldRemind = 
      this.reminderSettings.reminderTimes.includes(currentTime) &&
      dailyProgress.completed < this.reminderSettings.dailyGoal &&
      stats.totalPending > 0;
    
    return {
      shouldRemind,
      message: shouldRemind ? this.generateReminderMessage(dailyProgress, stats) : null,
      urgentCount: stats.byPriority[REVIEW_PRIORITY.URGENT] || 0,
      dailyProgress
    };
  }

  /**
   * 生成提醒消息
   * @param {Object} dailyProgress - 每日进度
   * @param {Object} stats - 复习统计
   * @returns {string} 提醒消息
   */
  generateReminderMessage(dailyProgress, stats) {
    const remaining = this.reminderSettings.dailyGoal - dailyProgress.completed;
    const urgentCount = stats.byPriority[REVIEW_PRIORITY.URGENT] || 0;
    
    if (urgentCount > 0) {
      return `您有 ${urgentCount} 个紧急复习项目！建议立即复习以避免遗忘。`;
    }
    
    if (remaining > 0) {
      return `今日还需复习 ${remaining} 个句子才能达成目标。加油！`;
    }
    
    return '您今天的复习目标已完成，可以选择额外复习来巩固记忆。';
  }

  /**
   * 清除所有数据
   */
  clear() {
    Object.keys(this.reviewQueues).forEach(priority => {
      this.reviewQueues[priority] = [];
    });
    this.scheduledReviews.clear();
    this.reviewHistory.clear();
  }
}

// 导出单例实例
export const reviewQueueManager = new ReviewQueueManager();
