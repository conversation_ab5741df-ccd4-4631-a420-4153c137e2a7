/**
 * 数据库API层
 * 提供统一的数据访问接口，封装所有数据库操作
 */

import { databaseManager } from './database.js';
import { sentenceRepository } from './sentenceRepository.js';
import { dataMigrationManager } from './dataMigration.js';

/**
 * 数据库API类
 */
export class DatabaseAPI {
  constructor() {
    this.db = databaseManager;
    this.sentenceRepo = sentenceRepository;
    this.migrationManager = dataMigrationManager;
    this.isReady = false;
  }

  /**
   * 初始化数据库API
   */
  async initialize() {
    try {
      // 初始化数据库
      await this.db.initialize();
      
      // 检查是否需要数据迁移
      const migrationStatus = this.migrationManager.checkMigrationStatus();
      if (!migrationStatus.isMigrated) {
        console.log('检测到首次使用，开始数据迁移...');
        await this.migrationManager.migrateAll();
      }
      
      this.isReady = true;
      console.log('数据库API初始化完成');
      return true;
    } catch (error) {
      console.error('数据库API初始化失败:', error);
      return false;
    }
  }

  /**
   * 检查API是否就绪
   */
  checkReady() {
    if (!this.isReady) {
      throw new Error('数据库API未初始化，请先调用initialize()');
    }
  }

  // ==================== 句子相关API ====================

  /**
   * 获取所有句子
   * @param {Object} options - 查询选项
   * @returns {Array} 句子数组
   */
  async getSentences(options = {}) {
    this.checkReady();
    return this.sentenceRepo.getAllSentences(options);
  }

  /**
   * 根据ID获取句子
   * @param {number} id - 句子ID
   * @returns {Object|null} 句子对象
   */
  async getSentenceById(id) {
    this.checkReady();
    return this.sentenceRepo.getSentenceById(id);
  }

  /**
   * 搜索句子
   * @param {string} keyword - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Array} 搜索结果
   */
  async searchSentences(keyword, options = {}) {
    this.checkReady();
    return this.sentenceRepo.searchSentences(keyword, options);
  }

  /**
   * 获取随机句子
   * @param {Object} options - 选项
   * @returns {Object|null} 随机句子
   */
  async getRandomSentence(options = {}) {
    this.checkReady();
    return this.sentenceRepo.getRandomSentence(options);
  }

  /**
   * 添加句子
   * @param {Object} sentence - 句子对象
   * @returns {number} 新增句子的ID
   */
  async addSentence(sentence) {
    this.checkReady();
    return this.sentenceRepo.addSentence(sentence);
  }

  /**
   * 更新句子
   * @param {number} id - 句子ID
   * @param {Object} updates - 更新数据
   * @returns {boolean} 是否更新成功
   */
  async updateSentence(id, updates) {
    this.checkReady();
    return this.sentenceRepo.updateSentence(id, updates);
  }

  /**
   * 删除句子
   * @param {number} id - 句子ID
   * @returns {boolean} 是否删除成功
   */
  async deleteSentence(id) {
    this.checkReady();
    return this.sentenceRepo.deleteSentence(id);
  }

  // ==================== 学习记录相关API ====================

  /**
   * 记录学习活动
   * @param {Object} record - 学习记录
   * @returns {number} 记录ID
   */
  async recordLearningActivity(record) {
    this.checkReady();
    
    const sql = `
      INSERT INTO learning_records (
        sentence_id, is_correct, response_time, mastery_level, 
        memory_strength, last_reviewed, next_review, 
        correct_count, total_attempts
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      record.sentenceId,
      record.isCorrect ? 1 : 0,
      record.responseTime || 0,
      record.masteryLevel || 0,
      record.memoryStrength || 0,
      new Date().toISOString(),
      record.nextReview || null,
      record.correctCount || 0,
      record.totalAttempts || 0
    ];
    
    const result = this.db.run(sql, params);
    return result.lastInsertRowid;
  }

  /**
   * 获取句子的学习记录
   * @param {number} sentenceId - 句子ID
   * @returns {Object|null} 学习记录
   */
  async getLearningRecord(sentenceId) {
    this.checkReady();
    
    const sql = `
      SELECT * FROM learning_records 
      WHERE sentence_id = ? 
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    const results = this.db.query(sql, [sentenceId]);
    return results.length > 0 ? results[0] : null;
  }

  /**
   * 更新学习记录
   * @param {number} sentenceId - 句子ID
   * @param {Object} updates - 更新数据
   * @returns {boolean} 是否更新成功
   */
  async updateLearningRecord(sentenceId, updates) {
    this.checkReady();
    
    // 先检查是否存在记录
    const existingRecord = await this.getLearningRecord(sentenceId);
    
    if (existingRecord) {
      // 更新现有记录
      const fields = [];
      const params = [];
      
      for (const [key, value] of Object.entries(updates)) {
        fields.push(`${this.camelToSnake(key)} = ?`);
        params.push(value);
      }
      
      params.push(sentenceId);
      
      const sql = `
        UPDATE learning_records 
        SET ${fields.join(', ')}, created_at = CURRENT_TIMESTAMP
        WHERE sentence_id = ?
      `;
      
      const result = this.db.run(sql, params);
      return result.changes > 0;
    } else {
      // 创建新记录
      return await this.recordLearningActivity({
        sentenceId,
        ...updates
      });
    }
  }

  // ==================== 用户偏好相关API ====================

  /**
   * 获取用户偏好
   * @param {string} key - 偏好键
   * @returns {any} 偏好值
   */
  async getUserPreference(key) {
    this.checkReady();
    
    const sql = 'SELECT value FROM user_preferences WHERE key = ?';
    const results = this.db.query(sql, [key]);
    
    if (results.length === 0) return null;
    
    try {
      return JSON.parse(results[0].value);
    } catch (error) {
      return results[0].value;
    }
  }

  /**
   * 设置用户偏好
   * @param {string} key - 偏好键
   * @param {any} value - 偏好值
   * @returns {boolean} 是否设置成功
   */
  async setUserPreference(key, value) {
    this.checkReady();
    
    const sql = `
      INSERT OR REPLACE INTO user_preferences (key, value, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `;
    
    const result = this.db.run(sql, [key, JSON.stringify(value)]);
    return result.changes > 0;
  }

  /**
   * 获取所有用户偏好
   * @returns {Object} 偏好对象
   */
  async getAllUserPreferences() {
    this.checkReady();
    
    const sql = 'SELECT key, value FROM user_preferences';
    const results = this.db.query(sql);
    
    const preferences = {};
    for (const row of results) {
      try {
        preferences[row.key] = JSON.parse(row.value);
      } catch (error) {
        preferences[row.key] = row.value;
      }
    }
    
    return preferences;
  }

  // ==================== 统计相关API ====================

  /**
   * 获取学习统计
   * @returns {Object} 统计信息
   */
  async getLearningStatistics() {
    this.checkReady();
    
    const stats = {};
    
    // 总体统计
    const totalResult = this.db.query(`
      SELECT 
        COUNT(*) as total_records,
        SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
        AVG(response_time) as avg_response_time
      FROM learning_records
    `);
    
    if (totalResult.length > 0) {
      const row = totalResult[0];
      stats.totalRecords = row.total_records || 0;
      stats.correctCount = row.correct_count || 0;
      stats.accuracy = stats.totalRecords > 0 ? (stats.correctCount / stats.totalRecords) : 0;
      stats.averageResponseTime = row.avg_response_time || 0;
    }
    
    // 按难度统计
    const difficultyStats = this.db.query(`
      SELECT 
        s.difficulty,
        COUNT(*) as total,
        SUM(CASE WHEN lr.is_correct = 1 THEN 1 ELSE 0 END) as correct
      FROM learning_records lr
      JOIN sentences s ON lr.sentence_id = s.id
      GROUP BY s.difficulty
    `);
    
    stats.byDifficulty = {};
    for (const row of difficultyStats) {
      stats.byDifficulty[row.difficulty] = {
        total: row.total,
        correct: row.correct,
        accuracy: row.total > 0 ? (row.correct / row.total) : 0
      };
    }
    
    return stats;
  }

  /**
   * 获取数据库统计信息
   * @returns {Object} 数据库统计
   */
  async getDatabaseStatistics() {
    this.checkReady();
    return this.db.getStats();
  }

  // ==================== 工具方法 ====================

  /**
   * 驼峰命名转下划线命名
   * @param {string} str - 驼峰命名字符串
   * @returns {string} 下划线命名字符串
   */
  camelToSnake(str) {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * 清空所有数据
   */
  async clearAllData() {
    this.checkReady();
    this.db.clear();
  }

  /**
   * 导出数据库
   * @returns {Uint8Array} 数据库二进制数据
   */
  async exportDatabase() {
    this.checkReady();
    return this.db.db.export();
  }

  /**
   * 备份数据到localStorage
   */
  async backupToStorage() {
    this.checkReady();
    this.db.saveToStorage();
  }
}

// 导出单例实例
export const databaseAPI = new DatabaseAPI();
