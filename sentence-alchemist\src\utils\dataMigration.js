/**
 * 数据迁移工具
 * 将现有的JSON格式数据迁移到SQLite数据库
 */

import { databaseManager } from './database.js';
import { sentenceRepository } from './sentenceRepository.js';
import { sampleSentences } from '../data/sampleSentences.js';

/**
 * 数据迁移管理器
 */
export class DataMigrationManager {
  constructor() {
    this.db = databaseManager;
    this.sentenceRepo = sentenceRepository;
  }

  /**
   * 执行完整的数据迁移
   */
  async migrateAll() {
    try {
      console.log('开始数据迁移...');
      
      // 确保数据库已初始化
      if (!this.db.isInitialized) {
        await this.db.initialize();
      }

      // 迁移句子数据
      await this.migrateSentences();
      
      // 迁移学习记录
      await this.migrateLearningRecords();
      
      // 迁移用户偏好
      await this.migrateUserPreferences();

      console.log('数据迁移完成！');
      return true;
    } catch (error) {
      console.error('数据迁移失败:', error);
      return false;
    }
  }

  /**
   * 迁移句子数据
   */
  async migrateSentences() {
    console.log('迁移句子数据...');
    
    // 检查是否已有数据
    const existingSentences = this.sentenceRepo.getAllSentences();
    if (existingSentences.length > 0) {
      console.log('句子数据已存在，跳过迁移');
      return;
    }

    // 扩展现有句子数据，添加缺失字段
    const enhancedSentences = this.enhanceSentenceData(sampleSentences);
    
    // 批量插入句子
    try {
      const insertedIds = this.sentenceRepo.addSentences(enhancedSentences);
      console.log(`成功迁移 ${insertedIds.length} 个句子`);
    } catch (error) {
      console.error('句子数据迁移失败:', error);
      throw error;
    }
  }

  /**
   * 增强句子数据，添加缺失的字段
   * @param {Array} sentences - 原始句子数组
   * @returns {Array} 增强后的句子数组
   */
  enhanceSentenceData(sentences) {
    return sentences.map(sentence => ({
      ...sentence,
      // 添加音标（IPA国际音标）
      pronunciation: this.generatePronunciation(sentence.english),
      // 添加中文发音提示
      chinesePronunciation: this.generateChinesePronunciation(sentence.english),
      // 添加标签
      tags: this.generateTags(sentence),
      // 添加语法解释
      explanation: this.generateExplanation(sentence),
      // 添加语法要点
      grammarPoints: this.generateGrammarPoints(sentence)
    }));
  }

  /**
   * 生成音标
   * @param {string} english - 英文句子
   * @returns {string} 音标
   */
  generatePronunciation(english) {
    // 简化的音标生成（实际应用中可以使用专业的音标API）
    const pronunciationMap = {
      "Can I have a window seat, please?": "/kæn aɪ hæv ə ˈwɪndoʊ sit, pliːz/",
      "How much does this cost?": "/haʊ mʌtʃ dʌz ðɪs kɔːst/",
      "Could you recommend a good restaurant nearby?": "/kʊd juː ˌrekəˈmend ə ɡʊd ˈrestərɑːnt ˈnɪrbaɪ/",
      "I'd like to check in, please.": "/aɪd laɪk tuː tʃek ɪn, pliːz/",
      "What time does the meeting start?": "/wʌt taɪm dʌz ðə ˈmiːtɪŋ stɑːrt/"
    };
    
    return pronunciationMap[english] || this.generateBasicPronunciation(english);
  }

  /**
   * 生成基础音标
   * @param {string} english - 英文句子
   * @returns {string} 基础音标
   */
  generateBasicPronunciation(english) {
    // 简化的音标生成逻辑
    return `/${english.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ')}/`;
  }

  /**
   * 生成中文发音提示
   * @param {string} english - 英文句子
   * @returns {string} 中文发音提示
   */
  generateChinesePronunciation(english) {
    const pronunciationMap = {
      "Can I have a window seat, please?": "坎 爱 哈夫 额 温多 西特，普利兹",
      "How much does this cost?": "豪 马奇 达斯 迪斯 考斯特",
      "Could you recommend a good restaurant nearby?": "库德 优 瑞肯门德 额 古德 瑞斯特朗特 尼尔拜",
      "I'd like to check in, please.": "爱德 莱克 图 切克 因，普利兹",
      "What time does the meeting start?": "沃特 泰姆 达斯 德 米听 斯塔特"
    };
    
    return pronunciationMap[english] || '';
  }

  /**
   * 生成标签
   * @param {Object} sentence - 句子对象
   * @returns {Array} 标签数组
   */
  generateTags(sentence) {
    const tags = [];
    
    // 基于场景添加标签
    const sceneTagMap = {
      'airport': ['旅行', '交通', '服务'],
      'shopping': ['购物', '价格', '询问'],
      'travel': ['旅行', '推荐', '地点'],
      'hotel': ['住宿', '服务', '登记'],
      'work': ['工作', '会议', '时间'],
      'restaurant': ['餐饮', '点餐', '服务']
    };
    
    if (sceneTagMap[sentence.scene]) {
      tags.push(...sceneTagMap[sentence.scene]);
    }
    
    // 基于难度添加标签
    if (sentence.difficulty === 'beginner') {
      tags.push('基础', '常用');
    } else if (sentence.difficulty === 'intermediate') {
      tags.push('中级', '实用');
    } else if (sentence.difficulty === 'advanced') {
      tags.push('高级', '复杂');
    }
    
    // 基于句子内容添加标签
    if (sentence.english.includes('please')) {
      tags.push('礼貌用语');
    }
    if (sentence.english.includes('?')) {
      tags.push('疑问句');
    }
    if (sentence.english.includes('could') || sentence.english.includes('would')) {
      tags.push('委婉表达');
    }
    
    return [...new Set(tags)]; // 去重
  }

  /**
   * 生成句子解释
   * @param {Object} sentence - 句子对象
   * @returns {string} 解释
   */
  generateExplanation(sentence) {
    const explanationMap = {
      "Can I have a window seat, please?": "这是在飞机或火车上请求靠窗座位的礼貌表达。'Can I have...'是常用的请求句型，'please'使语气更加礼貌。",
      "How much does this cost?": "询问价格的标准表达。'How much'用于询问不可数名词的数量或价格，'does'是助动词，用于第三人称单数。",
      "Could you recommend a good restaurant nearby?": "请求推荐的礼貌表达。'Could you'比'Can you'更加礼貌，'nearby'表示附近的。",
      "I'd like to check in, please.": "酒店入住登记的标准表达。'I'd like to'是'I would like to'的缩写，表示礼貌的请求。",
      "What time does the meeting start?": "询问时间的疑问句。'What time'用于询问具体时间，'does'用于第三人称单数的疑问句。"
    };
    
    return explanationMap[sentence.english] || `这是一个${sentence.difficulty}级别的${sentence.scene}场景常用表达。`;
  }

  /**
   * 生成语法要点
   * @param {Object} sentence - 句子对象
   * @returns {Array} 语法要点数组
   */
  generateGrammarPoints(sentence) {
    const grammarPoints = [];
    
    if (sentence.english.includes('Can I')) {
      grammarPoints.push('情态动词can的用法', '请求许可的表达');
    }
    if (sentence.english.includes('How much')) {
      grammarPoints.push('疑问词how much的用法', '询问价格的句型');
    }
    if (sentence.english.includes('Could you')) {
      grammarPoints.push('情态动词could的礼貌用法', '请求帮助的表达');
    }
    if (sentence.english.includes("I'd like")) {
      grammarPoints.push('would like的用法', '礼貌请求的表达');
    }
    if (sentence.english.includes('What time')) {
      grammarPoints.push('疑问词what time的用法', '询问时间的句型');
    }
    
    return grammarPoints;
  }

  /**
   * 迁移学习记录
   */
  async migrateLearningRecords() {
    console.log('迁移学习记录...');
    
    // 从localStorage获取现有学习记录
    const learningTrackerData = localStorage.getItem('sentence-alchemist-learning-tracker');
    if (!learningTrackerData) {
      console.log('没有找到学习记录数据');
      return;
    }

    try {
      const data = JSON.parse(learningTrackerData);
      
      // 迁移学习会话
      if (data.sessionHistory) {
        for (const [sessionId, session] of data.sessionHistory) {
          this.migrateLearningSession(sessionId, session);
        }
      }
      
      console.log('学习记录迁移完成');
    } catch (error) {
      console.error('学习记录迁移失败:', error);
    }
  }

  /**
   * 迁移单个学习会话
   * @param {string} sessionId - 会话ID
   * @param {Object} session - 会话数据
   */
  migrateLearningSession(sessionId, session) {
    const sql = `
      INSERT OR REPLACE INTO learning_sessions 
      (id, activity_type, start_time, end_time, total_questions, correct_answers, accuracy, config)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      sessionId,
      session.activityType,
      session.startTime,
      session.endTime,
      session.stats?.totalQuestions || 0,
      session.stats?.correctAnswers || 0,
      session.stats?.accuracy || 0,
      JSON.stringify(session.config || {})
    ];
    
    this.db.run(sql, params);
  }

  /**
   * 迁移用户偏好
   */
  async migrateUserPreferences() {
    console.log('迁移用户偏好...');
    
    // 从localStorage获取用户偏好
    const preferences = localStorage.getItem('userPreferences');
    const profile = localStorage.getItem('sentence-alchemist-profile');
    
    if (preferences) {
      try {
        const prefData = JSON.parse(preferences);
        for (const [key, value] of Object.entries(prefData)) {
          this.setUserPreference(key, value);
        }
      } catch (error) {
        console.error('用户偏好迁移失败:', error);
      }
    }
    
    if (profile) {
      try {
        const profileData = JSON.parse(profile);
        for (const [key, value] of Object.entries(profileData)) {
          this.setUserPreference(`profile_${key}`, value);
        }
      } catch (error) {
        console.error('用户档案迁移失败:', error);
      }
    }
    
    console.log('用户偏好迁移完成');
  }

  /**
   * 设置用户偏好
   * @param {string} key - 键
   * @param {any} value - 值
   */
  setUserPreference(key, value) {
    const sql = `
      INSERT OR REPLACE INTO user_preferences (key, value, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `;
    
    this.db.run(sql, [key, JSON.stringify(value)]);
  }

  /**
   * 检查迁移状态
   * @returns {Object} 迁移状态
   */
  checkMigrationStatus() {
    const stats = this.db.getStats();
    return {
      sentences: stats?.sentences || 0,
      learningRecords: stats?.learning_records || 0,
      learningSessions: stats?.learning_sessions || 0,
      userPreferences: stats?.user_preferences || 0,
      isMigrated: (stats?.sentences || 0) > 0
    };
  }
}

// 导出单例实例
export const dataMigrationManager = new DataMigrationManager();
