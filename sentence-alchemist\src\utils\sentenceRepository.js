/**
 * 句子数据访问层
 * 提供句子、俚语、单词等数据的CRUD操作
 */

import { databaseManager } from './database.js';

/**
 * 句子仓库类
 */
export class SentenceRepository {
  constructor() {
    this.db = databaseManager;
  }

  /**
   * 添加句子
   * @param {Object} sentence - 句子对象
   * @returns {number} 新增句子的ID
   */
  addSentence(sentence) {
    const sql = `
      INSERT INTO sentences (
        english, chinese, pronunciation, chinese_pronunciation,
        scene, difficulty, tags, explanation, grammar_points
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      sentence.english,
      sentence.chinese,
      sentence.pronunciation || null,
      sentence.chinesePronunciation || null,
      sentence.scene,
      sentence.difficulty,
      JSON.stringify(sentence.tags || []),
      sentence.explanation || null,
      JSON.stringify(sentence.grammarPoints || [])
    ];

    const result = this.db.run(sql, params);
    return result.lastInsertRowid;
  }

  /**
   * 批量添加句子
   * @param {Array} sentences - 句子数组
   * @returns {Array} 新增句子的ID数组
   */
  addSentences(sentences) {
    const ids = [];
    
    this.db.beginTransaction();
    try {
      for (const sentence of sentences) {
        const id = this.addSentence(sentence);
        ids.push(id);
      }
      this.db.commit();
      return ids;
    } catch (error) {
      this.db.rollback();
      throw error;
    }
  }

  /**
   * 根据ID获取句子
   * @param {number} id - 句子ID
   * @returns {Object|null} 句子对象
   */
  getSentenceById(id) {
    const sql = 'SELECT * FROM sentences WHERE id = ?';
    const results = this.db.query(sql, [id]);
    
    if (results.length === 0) return null;
    
    return this.formatSentence(results[0]);
  }

  /**
   * 获取所有句子
   * @param {Object} options - 查询选项
   * @returns {Array} 句子数组
   */
  getAllSentences(options = {}) {
    let sql = 'SELECT * FROM sentences';
    const params = [];
    const conditions = [];

    // 场景筛选
    if (options.scene) {
      conditions.push('scene = ?');
      params.push(options.scene);
    }

    // 难度筛选
    if (options.difficulty) {
      conditions.push('difficulty = ?');
      params.push(options.difficulty);
    }

    // 标签筛选
    if (options.tag) {
      conditions.push('tags LIKE ?');
      params.push(`%"${options.tag}"%`);
    }

    // 添加条件
    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ');
    }

    // 排序
    if (options.orderBy) {
      sql += ` ORDER BY ${options.orderBy}`;
      if (options.order === 'DESC') {
        sql += ' DESC';
      }
    }

    // 限制数量
    if (options.limit) {
      sql += ` LIMIT ${options.limit}`;
      if (options.offset) {
        sql += ` OFFSET ${options.offset}`;
      }
    }

    const results = this.db.query(sql, params);
    return results.map(row => this.formatSentence(row));
  }

  /**
   * 搜索句子
   * @param {string} keyword - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Array} 搜索结果
   */
  searchSentences(keyword, options = {}) {
    const sql = `
      SELECT * FROM sentences 
      WHERE english LIKE ? OR chinese LIKE ? OR explanation LIKE ?
      ORDER BY 
        CASE 
          WHEN english LIKE ? THEN 1
          WHEN chinese LIKE ? THEN 2
          ELSE 3
        END,
        id
      ${options.limit ? `LIMIT ${options.limit}` : ''}
    `;
    
    const searchTerm = `%${keyword}%`;
    const exactTerm = `${keyword}%`;
    const params = [searchTerm, searchTerm, searchTerm, exactTerm, exactTerm];

    const results = this.db.query(sql, params);
    return results.map(row => this.formatSentence(row));
  }

  /**
   * 更新句子
   * @param {number} id - 句子ID
   * @param {Object} updates - 更新数据
   * @returns {boolean} 是否更新成功
   */
  updateSentence(id, updates) {
    const fields = [];
    const params = [];

    // 构建更新字段
    for (const [key, value] of Object.entries(updates)) {
      if (key === 'tags' || key === 'grammarPoints') {
        fields.push(`${this.camelToSnake(key)} = ?`);
        params.push(JSON.stringify(value));
      } else {
        fields.push(`${this.camelToSnake(key)} = ?`);
        params.push(value);
      }
    }

    if (fields.length === 0) return false;

    fields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    const sql = `UPDATE sentences SET ${fields.join(', ')} WHERE id = ?`;
    const result = this.db.run(sql, params);
    
    return result.changes > 0;
  }

  /**
   * 删除句子
   * @param {number} id - 句子ID
   * @returns {boolean} 是否删除成功
   */
  deleteSentence(id) {
    const sql = 'DELETE FROM sentences WHERE id = ?';
    const result = this.db.run(sql, [id]);
    return result.changes > 0;
  }

  /**
   * 获取句子统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    const stats = {};

    // 总数统计
    const totalResult = this.db.query('SELECT COUNT(*) as total FROM sentences');
    stats.total = totalResult[0]?.total || 0;

    // 按场景统计
    const sceneResults = this.db.query(`
      SELECT scene, COUNT(*) as count 
      FROM sentences 
      GROUP BY scene 
      ORDER BY count DESC
    `);
    stats.byScene = sceneResults.reduce((acc, row) => {
      acc[row.scene] = row.count;
      return acc;
    }, {});

    // 按难度统计
    const difficultyResults = this.db.query(`
      SELECT difficulty, COUNT(*) as count 
      FROM sentences 
      GROUP BY difficulty 
      ORDER BY 
        CASE difficulty 
          WHEN 'beginner' THEN 1 
          WHEN 'intermediate' THEN 2 
          WHEN 'advanced' THEN 3 
          ELSE 4 
        END
    `);
    stats.byDifficulty = difficultyResults.reduce((acc, row) => {
      acc[row.difficulty] = row.count;
      return acc;
    }, {});

    return stats;
  }

  /**
   * 获取随机句子
   * @param {Object} options - 选项
   * @returns {Object|null} 随机句子
   */
  getRandomSentence(options = {}) {
    let sql = 'SELECT * FROM sentences';
    const params = [];
    const conditions = [];

    // 排除指定ID
    if (options.excludeIds && options.excludeIds.length > 0) {
      const placeholders = options.excludeIds.map(() => '?').join(',');
      conditions.push(`id NOT IN (${placeholders})`);
      params.push(...options.excludeIds);
    }

    // 场景筛选
    if (options.scene) {
      conditions.push('scene = ?');
      params.push(options.scene);
    }

    // 难度筛选
    if (options.difficulty) {
      if (Array.isArray(options.difficulty)) {
        const placeholders = options.difficulty.map(() => '?').join(',');
        conditions.push(`difficulty IN (${placeholders})`);
        params.push(...options.difficulty);
      } else {
        conditions.push('difficulty = ?');
        params.push(options.difficulty);
      }
    }

    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ');
    }

    sql += ' ORDER BY RANDOM() LIMIT 1';

    const results = this.db.query(sql, params);
    return results.length > 0 ? this.formatSentence(results[0]) : null;
  }

  /**
   * 格式化句子对象
   * @param {Object} row - 数据库行
   * @returns {Object} 格式化后的句子对象
   */
  formatSentence(row) {
    return {
      id: row.id,
      english: row.english,
      chinese: row.chinese,
      pronunciation: row.pronunciation,
      chinesePronunciation: row.chinese_pronunciation,
      scene: row.scene,
      difficulty: row.difficulty,
      tags: row.tags ? JSON.parse(row.tags) : [],
      explanation: row.explanation,
      grammarPoints: row.grammar_points ? JSON.parse(row.grammar_points) : [],
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  /**
   * 驼峰命名转下划线命名
   * @param {string} str - 驼峰命名字符串
   * @returns {string} 下划线命名字符串
   */
  camelToSnake(str) {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}

// 导出单例实例
export const sentenceRepository = new SentenceRepository();
