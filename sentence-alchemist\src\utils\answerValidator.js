/**
 * 答案验证逻辑系统
 * 处理用户答案的验证、评分和反馈生成
 */

// 答案结果类型
export const ANSWER_RESULT = {
  CORRECT: 'correct',
  INCORRECT: 'incorrect',
  PARTIAL: 'partial' // 部分正确（未来扩展用）
};

// 反馈类型
export const FEEDBACK_TYPE = {
  IMMEDIATE: 'immediate',
  DETAILED: 'detailed',
  ENCOURAGING: 'encouraging',
  CORRECTIVE: 'corrective'
};

/**
 * 答案验证器类
 */
export class AnswerValidator {
  constructor() {
    this.validationHistory = [];
  }

  /**
   * 验证用户答案
   * @param {Object} question - 问题对象
   * @param {number} userAnswerIndex - 用户选择的答案索引
   * @param {string[]} options - 选项数组
   * @param {number} correctIndex - 正确答案索引
   * @param {number} responseTime - 响应时间（毫秒）
   * @returns {Object} 验证结果
   */
  validateAnswer(question, userAnswerIndex, options, correctIndex, responseTime = 0) {
    // 添加安全检查
    if (!question) {
      throw new Error('Question is required for answer validation');
    }

    if (!options || !Array.isArray(options) || options.length === 0) {
      throw new Error('Valid options array is required');
    }

    if (typeof userAnswerIndex !== 'number' || userAnswerIndex < 0 || userAnswerIndex >= options.length) {
      throw new Error(`Invalid user answer index: ${userAnswerIndex}`);
    }

    if (typeof correctIndex !== 'number' || correctIndex < 0 || correctIndex >= options.length) {
      throw new Error(`Invalid correct answer index: ${correctIndex}`);
    }

    const isCorrect = userAnswerIndex === correctIndex;
    const userAnswer = options[userAnswerIndex];
    const correctAnswer = options[correctIndex];

    // 创建验证结果
    const result = {
      isCorrect,
      result: isCorrect ? ANSWER_RESULT.CORRECT : ANSWER_RESULT.INCORRECT,
      userAnswer,
      correctAnswer,
      userAnswerIndex,
      correctIndex,
      responseTime,
      timestamp: new Date(),
      questionId: question.id,
      scene: question.scene,
      difficulty: question.difficulty,
      score: this.calculateScore(isCorrect, responseTime, question.difficulty),
      feedback: this.generateFeedback(isCorrect, question, userAnswer, correctAnswer, responseTime)
    };

    // 记录验证历史
    this.validationHistory.push(result);

    // 更新问题的学习数据
    this.updateLearningData(question, result);

    return result;
  }

  /**
   * 计算得分
   * @param {boolean} isCorrect - 是否正确
   * @param {number} responseTime - 响应时间
   * @param {string} difficulty - 难度等级
   * @returns {number} 得分（0-100）
   */
  calculateScore(isCorrect, responseTime, difficulty) {
    if (!isCorrect) {
      return 0;
    }
    
    let baseScore = 100;
    
    // 根据难度调整基础分数
    const difficultyMultiplier = {
      'beginner': 1.0,
      'intermediate': 1.2,
      'advanced': 1.5
    };
    
    baseScore *= difficultyMultiplier[difficulty] || 1.0;
    
    // 根据响应时间调整分数
    if (responseTime > 0) {
      // 理想响应时间（毫秒）
      const idealTime = {
        'beginner': 5000,
        'intermediate': 8000,
        'advanced': 12000
      };
      
      const targetTime = idealTime[difficulty] || 8000;
      
      if (responseTime <= targetTime) {
        // 快速回答奖励
        const speedBonus = Math.max(0, (targetTime - responseTime) / targetTime * 20);
        baseScore += speedBonus;
      } else {
        // 慢速回答惩罚
        const speedPenalty = Math.min(30, (responseTime - targetTime) / targetTime * 15);
        baseScore -= speedPenalty;
      }
    }
    
    return Math.round(Math.max(10, Math.min(150, baseScore)));
  }

  /**
   * 生成反馈信息
   * @param {boolean} isCorrect - 是否正确
   * @param {Object} question - 问题对象
   * @param {string} userAnswer - 用户答案
   * @param {string} correctAnswer - 正确答案
   * @param {number} responseTime - 响应时间
   * @returns {Object} 反馈对象
   */
  generateFeedback(isCorrect, question, userAnswer, correctAnswer, responseTime) {
    const feedback = {
      type: isCorrect ? FEEDBACK_TYPE.ENCOURAGING : FEEDBACK_TYPE.CORRECTIVE,
      message: '',
      explanation: '',
      tips: [],
      encouragement: ''
    };
    
    if (isCorrect) {
      // 正确答案的反馈
      feedback.message = this.getCorrectFeedbackMessage(responseTime, question.difficulty);
      feedback.encouragement = this.getEncouragementMessage();
      
      if (question.explanation) {
        feedback.explanation = `补充说明：${question.explanation}`;
      }
    } else {
      // 错误答案的反馈
      feedback.message = '答案不正确，让我们一起学习！';
      feedback.explanation = `正确答案是："${correctAnswer}"`;
      
      if (question.explanation) {
        feedback.explanation += `\n\n解释：${question.explanation}`;
      }
      
      // 分析错误类型并给出建议
      feedback.tips = this.analyzeError(userAnswer, correctAnswer, question);
    }
    
    return feedback;
  }

  /**
   * 获取正确答案的反馈消息
   * @param {number} responseTime - 响应时间
   * @param {string} difficulty - 难度等级
   * @returns {string} 反馈消息
   */
  getCorrectFeedbackMessage(responseTime, difficulty) {
    const messages = {
      fast: ['太棒了！反应真快！', '优秀！你的速度很快！', '完美！快速且准确！'],
      normal: ['正确！做得很好！', '很好！答案正确！', '不错！继续保持！'],
      slow: ['正确！慢慢来，准确更重要！', '答对了！思考得很仔细！', '正确！稳扎稳打！']
    };
    
    let category = 'normal';
    
    if (responseTime > 0) {
      const thresholds = {
        'beginner': { fast: 3000, slow: 8000 },
        'intermediate': { fast: 5000, slow: 12000 },
        'advanced': { fast: 8000, slow: 15000 }
      };
      
      const threshold = thresholds[difficulty] || thresholds.intermediate;
      
      if (responseTime < threshold.fast) {
        category = 'fast';
      } else if (responseTime > threshold.slow) {
        category = 'slow';
      }
    }
    
    const categoryMessages = messages[category];
    return categoryMessages[Math.floor(Math.random() * categoryMessages.length)];
  }

  /**
   * 获取鼓励消息
   * @returns {string} 鼓励消息
   */
  getEncouragementMessage() {
    const messages = [
      '继续加油！你正在进步！',
      '学习英语就是这样一步步积累的！',
      '每一次正确都是进步的体现！',
      '你的英语水平在不断提高！',
      '坚持练习，你会越来越棒！'
    ];
    
    return messages[Math.floor(Math.random() * messages.length)];
  }

  /**
   * 分析错误类型并给出建议
   * @param {string} userAnswer - 用户答案
   * @param {string} correctAnswer - 正确答案
   * @param {Object} question - 问题对象
   * @returns {string[]} 建议数组
   */
  analyzeError(userAnswer, correctAnswer, question) {
    const tips = [];
    
    // 基于场景的建议
    const sceneTips = {
      'airport': '机场场景中要注意礼貌用语和专业术语的使用',
      'restaurant': '餐厅场景中注意区分预订、点餐和结账的不同表达',
      'hotel': '酒店场景中要熟悉入住、退房等专业术语',
      'shopping': '购物场景中要注意询问价格、尺码等常用表达',
      'work': '工作场景中要注意正式和礼貌的表达方式'
    };
    
    if (sceneTips[question.scene]) {
      tips.push(sceneTips[question.scene]);
    }
    
    // 基于难度的建议
    if (question.difficulty === 'intermediate' || question.difficulty === 'advanced') {
      tips.push('这是一个中高级句子，多练习几次就能掌握！');
    }
    
    // 基于标签的建议
    if (question.tags) {
      if (question.tags.includes('polite-request')) {
        tips.push('注意英语中礼貌请求的表达方式，如 "Could you..." 或 "Would you..."');
      }
      if (question.tags.includes('time-inquiry')) {
        tips.push('询问时间时要注意疑问词的使用，如 "What time" 或 "When"');
      }
    }
    
    // 通用建议
    tips.push('多读几遍英文原句，理解句子的整体含义');
    
    return tips;
  }

  /**
   * 更新问题的学习数据
   * @param {Object} question - 问题对象
   * @param {Object} result - 验证结果
   */
  updateLearningData(question, result) {
    if (!question.learningData) {
      question.learningData = {
        correctCount: 0,
        totalAttempts: 0,
        lastReviewed: null,
        nextReview: null,
        masteryLevel: 0
      };
    }
    
    const learningData = question.learningData;
    
    // 更新统计数据
    learningData.totalAttempts++;
    if (result.isCorrect) {
      learningData.correctCount++;
    }
    
    // 更新复习时间
    learningData.lastReviewed = new Date();
    
    // 计算掌握程度（0-5）
    const accuracy = learningData.correctCount / learningData.totalAttempts;
    if (accuracy >= 0.9 && learningData.totalAttempts >= 3) {
      learningData.masteryLevel = Math.min(5, learningData.masteryLevel + 1);
    } else if (accuracy < 0.5) {
      learningData.masteryLevel = Math.max(0, learningData.masteryLevel - 1);
    }
    
    // 设置下次复习时间（基于掌握程度）
    const reviewIntervals = [1, 3, 7, 14, 30]; // 天数
    const intervalDays = reviewIntervals[learningData.masteryLevel] || 1;
    learningData.nextReview = new Date(Date.now() + intervalDays * 24 * 60 * 60 * 1000);
  }

  /**
   * 获取验证历史统计
   * @param {number} limit - 限制返回的记录数
   * @returns {Object} 统计信息
   */
  getValidationStats(limit = 100) {
    const recentHistory = this.validationHistory.slice(-limit);
    
    if (recentHistory.length === 0) {
      return {
        totalAttempts: 0,
        correctCount: 0,
        accuracy: 0,
        averageScore: 0,
        averageResponseTime: 0
      };
    }
    
    const correctCount = recentHistory.filter(r => r.isCorrect).length;
    const totalScore = recentHistory.reduce((sum, r) => sum + r.score, 0);
    const totalResponseTime = recentHistory.reduce((sum, r) => sum + r.responseTime, 0);
    
    return {
      totalAttempts: recentHistory.length,
      correctCount,
      accuracy: correctCount / recentHistory.length,
      averageScore: totalScore / recentHistory.length,
      averageResponseTime: totalResponseTime / recentHistory.length
    };
  }

  /**
   * 清除验证历史
   */
  clearHistory() {
    this.validationHistory = [];
  }
}

// 导出单例实例
export const answerValidator = new AnswerValidator();
