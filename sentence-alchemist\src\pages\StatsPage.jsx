import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  BarChart3, 
  TrendingUp, 
  Target, 
  Clock, 
  Calendar,
  Award,
  Flame,
  Brain,
  Activity,
  CheckCircle
} from 'lucide-react';
import { learningTracker } from '../utils/learningTracker';
import { learningStateManager } from '../utils/learningStateManager';
import { sampleSentences } from '../data/sampleSentences';

const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  color: #2d3748;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;

const Subtitle = styled.p`
  color: #718096;
  font-size: 1.1rem;
  margin: 0;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const StatCard = styled.div`
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-left: 4px solid ${props => props.color || '#667eea'};
`;

const CardHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
`;

const CardTitle = styled.h3`
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const CardIcon = styled.div`
  color: ${props => props.color || '#667eea'};
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  color: #718096;
  margin-bottom: 1rem;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: ${props => props.color || '#667eea'};
  border-radius: 4px;
  transition: width 0.3s ease;
  width: ${props => props.progress}%;
`;

const ProgressText = styled.div`
  font-size: 0.8rem;
  color: #718096;
  text-align: right;
`;

const TrendSection = styled.div`
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
`;

const TrendTitle = styled.h2`
  color: #2d3748;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TrendGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
  margin-top: 1rem;
`;

const DayCell = styled.div`
  aspect-ratio: 1;
  background: ${props => {
    if (props.count === 0) return '#f7fafc';
    if (props.count < 5) return '#c6f6d5';
    if (props.count < 10) return '#9ae6b4';
    if (props.count < 20) return '#68d391';
    return '#38a169';
  }};
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  color: ${props => props.count > 10 ? 'white' : '#2d3748'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
`;

const AchievementSection = styled.div`
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
`;

const AchievementGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
`;

const AchievementCard = styled.div`
  background: ${props => props.earned ? 'linear-gradient(135deg, #ffd700, #ffed4e)' : '#f7fafc'};
  border: 2px solid ${props => props.earned ? '#d69e2e' : '#e2e8f0'};
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  opacity: ${props => props.earned ? 1 : 0.6};
`;

const AchievementIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const AchievementName = styled.div`
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
`;

const AchievementDesc = styled.div`
  font-size: 0.8rem;
  color: #718096;
`;

const StatsPage = ({ onSentenceDetail }) => {
  const [stats, setStats] = useState(null);
  const [learningStats, setLearningStats] = useState(null);
  const [weeklyData, setWeeklyData] = useState([]);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = () => {
    // 获取学习追踪统计
    const trackingStats = learningTracker.getLearningStats(30);
    
    // 获取学习状态统计
    const stateStats = learningStateManager.getLearningStats(sampleSentences);
    
    // 获取记忆强度分析
    const memoryAnalysis = learningStateManager.getMemoryStrengthAnalysis(sampleSentences);
    
    // 获取学习效率分析
    const efficiencyAnalysis = learningStateManager.getLearningEfficiencyAnalysis(sampleSentences);
    
    setStats(trackingStats);
    setLearningStats({
      ...stateStats,
      memoryAnalysis,
      efficiencyAnalysis
    });
    
    // 生成最近7天的数据
    generateWeeklyData(trackingStats);
  };

  const generateWeeklyData = (stats) => {
    const data = [];
    const today = new Date();
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
      const dateKey = date.toISOString().split('T')[0];
      
      // 从统计数据中查找对应日期的数据
      const dayData = stats.trends.dailyQuestions.find(d => d.date === dateKey);
      
      data.push({
        date: dateKey,
        day: date.getDate(),
        count: dayData ? dayData.count : 0
      });
    }
    
    setWeeklyData(data);
  };

  const achievements = [
    {
      id: 'first_steps',
      name: '初学者',
      description: '完成第一次练习',
      icon: '🎯',
      earned: stats?.totals.questions > 0
    },
    {
      id: 'streak_3',
      name: '坚持不懈',
      description: '连续学习3天',
      icon: '🔥',
      earned: stats?.streaks.current.count >= 3
    },
    {
      id: 'accuracy_master',
      name: '准确大师',
      description: '准确率达到90%以上',
      icon: '🎯',
      earned: stats?.averages.accuracy >= 0.9
    },
    {
      id: 'speed_demon',
      name: '闪电侠',
      description: '平均响应时间少于3秒',
      icon: '⚡',
      earned: stats?.averages.timePerQuestion < 3000
    },
    {
      id: 'century_club',
      name: '百题俱乐部',
      description: '累计答题100道',
      icon: '💯',
      earned: stats?.totals.questions >= 100
    },
    {
      id: 'week_warrior',
      name: '周战士',
      description: '连续学习7天',
      icon: '🏆',
      earned: stats?.streaks.current.count >= 7
    }
  ];

  if (!stats || !learningStats) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <div style={{ fontSize: '1.2rem', color: '#718096' }}>加载统计数据中...</div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <Header>
        <Title>
          <BarChart3 size={32} />
          学习统计
        </Title>
        <Subtitle>追踪您的学习进度和成就</Subtitle>
      </Header>

      <StatsGrid>
        <StatCard color="#667eea">
          <CardHeader>
            <CardTitle>
              <CardIcon color="#667eea">
                <Target size={20} />
              </CardIcon>
              今日学习
            </CardTitle>
          </CardHeader>
          <StatValue>{stats.trends.dailyQuestions[stats.trends.dailyQuestions.length - 1]?.count || 0}</StatValue>
          <StatLabel>道题目</StatLabel>
          <ProgressBar>
            <ProgressFill 
              progress={Math.min(100, (stats.trends.dailyQuestions[stats.trends.dailyQuestions.length - 1]?.count || 0) / 20 * 100)}
              color="#667eea"
            />
          </ProgressBar>
          <ProgressText>目标: 20题/天</ProgressText>
        </StatCard>

        <StatCard color="#38a169">
          <CardHeader>
            <CardTitle>
              <CardIcon color="#38a169">
                <CheckCircle size={20} />
              </CardIcon>
              总体准确率
            </CardTitle>
          </CardHeader>
          <StatValue>{(stats.averages.accuracy * 100).toFixed(1)}%</StatValue>
          <StatLabel>累计答题 {stats.totals.questions} 道</StatLabel>
          <ProgressBar>
            <ProgressFill 
              progress={stats.averages.accuracy * 100}
              color="#38a169"
            />
          </ProgressBar>
          <ProgressText>正确 {stats.totals.correctAnswers} 道</ProgressText>
        </StatCard>

        <StatCard color="#d69e2e">
          <CardHeader>
            <CardTitle>
              <CardIcon color="#d69e2e">
                <Flame size={20} />
              </CardIcon>
              学习连续
            </CardTitle>
          </CardHeader>
          <StatValue>{stats.streaks.current.count}</StatValue>
          <StatLabel>天连续学习</StatLabel>
          <ProgressBar>
            <ProgressFill 
              progress={Math.min(100, stats.streaks.current.count / 30 * 100)}
              color="#d69e2e"
            />
          </ProgressBar>
          <ProgressText>最长记录: {stats.streaks.max.count} 天</ProgressText>
        </StatCard>

        <StatCard color="#e53e3e">
          <CardHeader>
            <CardTitle>
              <CardIcon color="#e53e3e">
                <Clock size={20} />
              </CardIcon>
              学习时长
            </CardTitle>
          </CardHeader>
          <StatValue>{Math.round(stats.totals.timeSpent / 1000 / 60)}</StatValue>
          <StatLabel>分钟总时长</StatLabel>
          <ProgressBar>
            <ProgressFill 
              progress={Math.min(100, (stats.totals.timeSpent / 1000 / 60) / 300 * 100)}
              color="#e53e3e"
            />
          </ProgressBar>
          <ProgressText>平均 {Math.round(stats.averages.timePerDay / 1000 / 60)} 分钟/天</ProgressText>
        </StatCard>
      </StatsGrid>

      <TrendSection>
        <TrendTitle>
          <Activity size={20} />
          最近7天学习活动
        </TrendTitle>
        <TrendGrid>
          {weeklyData.map((day, index) => (
            <DayCell key={index} count={day.count}>
              {day.day}
            </DayCell>
          ))}
        </TrendGrid>
      </TrendSection>

      <AchievementSection>
        <TrendTitle>
          <Award size={20} />
          成就徽章
        </TrendTitle>
        <AchievementGrid>
          {achievements.map(achievement => (
            <AchievementCard key={achievement.id} earned={achievement.earned}>
              <AchievementIcon>{achievement.icon}</AchievementIcon>
              <AchievementName>{achievement.name}</AchievementName>
              <AchievementDesc>{achievement.description}</AchievementDesc>
            </AchievementCard>
          ))}
        </AchievementGrid>
      </AchievementSection>
    </PageContainer>
  );
};

export default StatsPage;
