/**
 * 数据库初始化组件
 * 负责初始化数据库并导入扩展数据
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Database, Download, CheckCircle, AlertCircle, Loader } from 'lucide-react';
import { databaseAPI } from '../utils/databaseAPI.js';
import { allExtendedData } from '../data/extendedSentences.js';

const InitializerContainer = styled.div`
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const Title = styled.h2`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2d3748;
  margin-bottom: 1.5rem;
`;

const StatusCard = styled.div`
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  
  ${props => {
    switch (props.status) {
      case 'loading':
        return `
          background: #ebf8ff;
          border: 1px solid #90cdf4;
          color: #2b6cb0;
        `;
      case 'success':
        return `
          background: #f0fff4;
          border: 1px solid #9ae6b4;
          color: #276749;
        `;
      case 'error':
        return `
          background: #fed7d7;
          border: 1px solid #fc8181;
          color: #c53030;
        `;
      default:
        return `
          background: #f7fafc;
          border: 1px solid #e2e8f0;
          color: #4a5568;
        `;
    }
  }}
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin: 1rem 0;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: #4299e1;
  border-radius: 4px;
  transition: width 0.3s ease;
  width: ${props => props.progress}%;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
`;

const StatCard = styled.div`
  text-align: center;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 8px;
`;

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: #2d3748;
`;

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: #718096;
  margin-top: 0.25rem;
`;

const ActionButton = styled.button`
  background: #4299e1;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  
  &:hover {
    background: #3182ce;
  }
  
  &:disabled {
    background: #a0aec0;
    cursor: not-allowed;
  }
`;

export default function DatabaseInitializer({ onComplete }) {
  const [initStatus, setInitStatus] = useState('idle'); // idle, loading, success, error
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [stats, setStats] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    checkDatabaseStatus();
  }, []);

  const checkDatabaseStatus = async () => {
    try {
      const dbStats = await databaseAPI.getDatabaseStatistics();
      setStats(dbStats);
      
      if (dbStats && dbStats.sentences > 0) {
        setInitStatus('success');
        setProgress(100);
      }
    } catch (error) {
      console.error('检查数据库状态失败:', error);
    }
  };

  const initializeDatabase = async () => {
    setInitStatus('loading');
    setProgress(0);
    setError(null);

    try {
      // 步骤1: 初始化数据库
      setCurrentStep('初始化数据库结构...');
      setProgress(10);
      
      const initialized = await databaseAPI.initialize();
      if (!initialized) {
        throw new Error('数据库初始化失败');
      }

      // 步骤2: 导入句子数据
      setCurrentStep('导入句子数据...');
      setProgress(30);
      
      for (let i = 0; i < allExtendedData.sentences.length; i++) {
        const sentence = allExtendedData.sentences[i];
        await databaseAPI.addSentence(sentence);
        setProgress(30 + (i / allExtendedData.sentences.length) * 40);
      }

      // 步骤3: 导入俚语数据
      setCurrentStep('导入俚语数据...');
      setProgress(70);
      
      // 这里可以添加俚语导入逻辑
      // for (const idiom of allExtendedData.idioms) {
      //   await databaseAPI.addIdiom(idiom);
      // }

      // 步骤4: 导入单词数据
      setCurrentStep('导入单词数据...');
      setProgress(85);
      
      // 这里可以添加单词导入逻辑
      // for (const word of allExtendedData.words) {
      //   await databaseAPI.addWord(word);
      // }

      // 步骤5: 完成
      setCurrentStep('初始化完成！');
      setProgress(100);
      setInitStatus('success');

      // 更新统计信息
      await checkDatabaseStatus();

      // 通知父组件初始化完成
      if (onComplete) {
        onComplete();
      }

    } catch (error) {
      console.error('数据库初始化失败:', error);
      setError(error.message);
      setInitStatus('error');
    }
  };

  const getStatusIcon = () => {
    switch (initStatus) {
      case 'loading':
        return <Loader className="animate-spin" size={20} />;
      case 'success':
        return <CheckCircle size={20} />;
      case 'error':
        return <AlertCircle size={20} />;
      default:
        return <Database size={20} />;
    }
  };

  const getStatusText = () => {
    switch (initStatus) {
      case 'loading':
        return currentStep || '正在初始化...';
      case 'success':
        return '数据库初始化成功！';
      case 'error':
        return `初始化失败: ${error}`;
      default:
        return '数据库未初始化';
    }
  };

  return (
    <InitializerContainer>
      <Title>
        <Database size={24} />
        数据库初始化
      </Title>

      <StatusCard status={initStatus}>
        {getStatusIcon()}
        <span>{getStatusText()}</span>
      </StatusCard>

      {initStatus === 'loading' && (
        <ProgressBar>
          <ProgressFill progress={progress} />
        </ProgressBar>
      )}

      {stats && (
        <StatsGrid>
          <StatCard>
            <StatValue>{stats.sentences || 0}</StatValue>
            <StatLabel>句子</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.idioms || 0}</StatValue>
            <StatLabel>俚语</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.words || 0}</StatValue>
            <StatLabel>单词</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{stats.learning_records || 0}</StatValue>
            <StatLabel>学习记录</StatLabel>
          </StatCard>
        </StatsGrid>
      )}

      {initStatus !== 'loading' && initStatus !== 'success' && (
        <ActionButton onClick={initializeDatabase}>
          <Download size={18} />
          开始初始化
        </ActionButton>
      )}

      {initStatus === 'success' && (
        <ActionButton onClick={() => window.location.reload()}>
          <CheckCircle size={18} />
          继续使用应用
        </ActionButton>
      )}
    </InitializerContainer>
  );
}
