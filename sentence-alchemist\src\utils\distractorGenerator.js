/**
 * 干扰项生成算法
 * 用于智能生成有效的干扰项，确保干扰项既有一定的迷惑性，又不会过于简单或困难
 */

// 常见的翻译错误类型
const ERROR_TYPES = {
  LITERAL_TRANSLATION: 'literal', // 直译错误
  WORD_ORDER: 'word_order', // 语序错误
  SIMILAR_MEANING: 'similar_meaning', // 相似含义
  GRAMMAR_ERROR: 'grammar_error', // 语法错误
  CONTEXT_ERROR: 'context_error' // 语境错误
};

// 常见的错误翻译模式
const TRANSLATION_PATTERNS = {
  // 直译模式
  literal: {
    'Can I': '我能',
    'Could you': '你能',
    'Would you': '你会',
    'I would like': '我会喜欢',
    'How much': '多少',
    'What time': '什么时间',
    'Where is': '哪里是'
  },
  
  // 语序错误模式
  wordOrder: {
    '什么时候开始会议？': '会议什么时候开始？',
    '多少钱这个？': '这个多少钱？',
    '在哪里登机口？': '登机口在哪里？'
  },
  
  // 语法错误模式
  grammar: {
    '我想要': '我想',
    '你能够': '你能',
    '我可以': '我能',
    '请给我': '给我'
  }
};

/**
 * 生成干扰项的主函数
 * @param {string} correctAnswer - 正确答案
 * @param {string} englishSentence - 英文原句
 * @param {string} scene - 场景
 * @param {string} difficulty - 难度等级
 * @returns {string[]} 生成的干扰项数组
 */
export function generateDistractors(correctAnswer, englishSentence, scene, difficulty) {
  const distractors = [];
  
  // 1. 生成直译错误
  const literalError = generateLiteralTranslation(englishSentence, correctAnswer);
  if (literalError && literalError !== correctAnswer) {
    distractors.push(literalError);
  }
  
  // 2. 生成语序错误
  const wordOrderError = generateWordOrderError(correctAnswer);
  if (wordOrderError && wordOrderError !== correctAnswer) {
    distractors.push(wordOrderError);
  }
  
  // 3. 生成相似含义错误
  const similarMeaningError = generateSimilarMeaning(correctAnswer, scene);
  if (similarMeaningError && similarMeaningError !== correctAnswer) {
    distractors.push(similarMeaningError);
  }
  
  // 4. 生成语法错误
  const grammarError = generateGrammarError(correctAnswer);
  if (grammarError && grammarError !== correctAnswer) {
    distractors.push(grammarError);
  }
  
  // 5. 如果生成的干扰项不够，使用场景相关的错误
  while (distractors.length < 3) {
    const contextError = generateContextError(correctAnswer, scene);
    if (contextError && contextError !== correctAnswer && !distractors.includes(contextError)) {
      distractors.push(contextError);
    } else {
      // 如果无法生成更多有效干扰项，使用通用错误
      const genericError = generateGenericError(correctAnswer, distractors.length);
      if (genericError && !distractors.includes(genericError)) {
        distractors.push(genericError);
      } else {
        break; // 避免无限循环
      }
    }
  }
  
  // 确保返回3个干扰项
  return distractors.slice(0, 3);
}

/**
 * 生成直译错误
 */
function generateLiteralTranslation(englishSentence, correctAnswer) {
  // 查找常见的直译错误模式
  for (const [english, literal] of Object.entries(TRANSLATION_PATTERNS.literal)) {
    if (englishSentence.includes(english)) {
      // 尝试替换为直译版本
      const literalVersion = correctAnswer.replace(/[你我]能/, literal);
      if (literalVersion !== correctAnswer) {
        return literalVersion;
      }
    }
  }
  
  // 生成一些通用的直译错误
  if (correctAnswer.includes('可以')) {
    return correctAnswer.replace('可以', '能够');
  }
  if (correctAnswer.includes('什么时候')) {
    return correctAnswer.replace('什么时候', '什么时间');
  }
  
  return null;
}

/**
 * 生成语序错误
 */
function generateWordOrderError(correctAnswer) {
  // 简单的语序调换
  if (correctAnswer.includes('什么时候')) {
    return correctAnswer.replace(/(.+)什么时候(.+)/, '$2什么时候$1');
  }
  if (correctAnswer.includes('多少钱')) {
    return correctAnswer.replace(/(.+)多少钱/, '多少钱$1');
  }
  if (correctAnswer.includes('在哪里')) {
    return correctAnswer.replace(/(.+)在哪里/, '在哪里$1');
  }
  
  return null;
}

/**
 * 生成相似含义错误
 */
function generateSimilarMeaning(correctAnswer, scene) {
  const synonyms = {
    '推荐': '建议',
    '预订': '预定',
    '办理': '处理',
    '账单': '票据',
    '座位': '位置',
    '房间': '房子',
    '尺码': '大小',
    '试穿': '试试'
  };
  
  for (const [original, synonym] of Object.entries(synonyms)) {
    if (correctAnswer.includes(original)) {
      return correctAnswer.replace(original, synonym);
    }
  }
  
  return null;
}

/**
 * 生成语法错误
 */
function generateGrammarError(correctAnswer) {
  // 常见的语法错误替换
  if (correctAnswer.includes('我想')) {
    return correctAnswer.replace('我想', '我要');
  }
  if (correctAnswer.includes('你能')) {
    return correctAnswer.replace('你能', '你可以');
  }
  if (correctAnswer.includes('请给我')) {
    return correctAnswer.replace('请给我', '给我');
  }
  
  return null;
}

/**
 * 生成语境错误
 */
function generateContextError(correctAnswer, scene) {
  const contextErrors = {
    airport: {
      '登机口': '登机门',
      '托运': '检查',
      '座位': '位子'
    },
    restaurant: {
      '预订': '保留',
      '推荐': '介绍',
      '账单': '清单'
    },
    hotel: {
      '入住': '登记',
      '退房': '结账',
      '房间': '屋子'
    },
    shopping: {
      '试穿': '尝试',
      '尺码': '尺寸',
      '多少钱': '什么价格'
    }
  };
  
  const sceneErrors = contextErrors[scene] || {};
  for (const [original, error] of Object.entries(sceneErrors)) {
    if (correctAnswer.includes(original)) {
      return correctAnswer.replace(original, error);
    }
  }
  
  return null;
}

/**
 * 生成通用错误（当其他方法都失败时使用）
 */
function generateGenericError(correctAnswer, index) {
  const genericPatterns = [
    (text) => text.replace('吗？', '？'),
    (text) => text.replace('。', '？'),
    (text) => text.replace('我', '我们'),
    (text) => text.replace('你', '您')
  ];
  
  if (index < genericPatterns.length) {
    return genericPatterns[index](correctAnswer);
  }
  
  return null;
}

/**
 * 验证干扰项质量
 * @param {string[]} distractors - 干扰项数组
 * @param {string} correctAnswer - 正确答案
 * @returns {boolean} 是否通过质量检查
 */
export function validateDistractors(distractors, correctAnswer) {
  // 检查是否有重复项
  const uniqueDistractors = [...new Set(distractors)];
  if (uniqueDistractors.length !== distractors.length) {
    return false;
  }
  
  // 检查是否包含正确答案
  if (distractors.includes(correctAnswer)) {
    return false;
  }
  
  // 检查长度差异是否合理（不应该相差太大）
  for (const distractor of distractors) {
    const lengthDiff = Math.abs(distractor.length - correctAnswer.length);
    if (lengthDiff > correctAnswer.length * 0.5) {
      return false;
    }
  }
  
  return true;
}
