/**
 * 数据库管理器 - 使用SQL.js实现SQLite数据库
 * 支持句子、俚语、音标等数据的存储和查询
 */

import initSqlJs from 'sql.js';

// 数据库表结构定义
const DATABASE_SCHEMA = {
  // 句子表
  sentences: `
    CREATE TABLE IF NOT EXISTS sentences (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      english TEXT NOT NULL,
      chinese TEXT NOT NULL,
      pronunciation TEXT,
      chinese_pronunciation TEXT,
      scene TEXT NOT NULL,
      difficulty TEXT NOT NULL,
      tags TEXT, -- JSON数组字符串
      explanation TEXT,
      grammar_points TEXT, -- JSON数组字符串
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `,
  
  // 俚语表
  idioms: `
    CREATE TABLE IF NOT EXISTS idioms (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      english TEXT NOT NULL,
      chinese TEXT NOT NULL,
      pronunciation TEXT,
      meaning TEXT,
      usage_example TEXT,
      origin TEXT,
      difficulty TEXT NOT NULL,
      tags TEXT, -- JSON数组字符串
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `,
  
  // 单词表
  words: `
    CREATE TABLE IF NOT EXISTS words (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      word TEXT NOT NULL UNIQUE,
      pronunciation TEXT,
      part_of_speech TEXT,
      meaning TEXT,
      usage TEXT,
      example_sentences TEXT, -- JSON数组字符串
      frequency INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `,
  
  // 学习记录表
  learning_records: `
    CREATE TABLE IF NOT EXISTS learning_records (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      sentence_id INTEGER,
      user_id TEXT DEFAULT 'default',
      is_correct BOOLEAN NOT NULL,
      response_time INTEGER,
      mastery_level INTEGER DEFAULT 0,
      memory_strength REAL DEFAULT 0,
      last_reviewed DATETIME,
      next_review DATETIME,
      correct_count INTEGER DEFAULT 0,
      total_attempts INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (sentence_id) REFERENCES sentences (id)
    );
  `,
  
  // 学习会话表
  learning_sessions: `
    CREATE TABLE IF NOT EXISTS learning_sessions (
      id TEXT PRIMARY KEY,
      user_id TEXT DEFAULT 'default',
      activity_type TEXT NOT NULL,
      start_time DATETIME NOT NULL,
      end_time DATETIME,
      total_questions INTEGER DEFAULT 0,
      correct_answers INTEGER DEFAULT 0,
      accuracy REAL DEFAULT 0,
      config TEXT, -- JSON字符串
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `,
  
  // 用户偏好表
  user_preferences: `
    CREATE TABLE IF NOT EXISTS user_preferences (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id TEXT DEFAULT 'default',
      key TEXT NOT NULL,
      value TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(user_id, key)
    );
  `
};

// 索引定义
const DATABASE_INDEXES = [
  'CREATE INDEX IF NOT EXISTS idx_sentences_scene ON sentences(scene);',
  'CREATE INDEX IF NOT EXISTS idx_sentences_difficulty ON sentences(difficulty);',
  'CREATE INDEX IF NOT EXISTS idx_sentences_english ON sentences(english);',
  'CREATE INDEX IF NOT EXISTS idx_learning_records_sentence_id ON learning_records(sentence_id);',
  'CREATE INDEX IF NOT EXISTS idx_learning_records_user_id ON learning_records(user_id);',
  'CREATE INDEX IF NOT EXISTS idx_words_word ON words(word);',
  'CREATE INDEX IF NOT EXISTS idx_idioms_difficulty ON idioms(difficulty);'
];

/**
 * 数据库管理器类
 */
export class DatabaseManager {
  constructor() {
    this.db = null;
    this.SQL = null;
    this.isInitialized = false;
  }

  /**
   * 初始化数据库
   */
  async initialize() {
    try {
      // 初始化SQL.js
      this.SQL = await initSqlJs({
        locateFile: file => `https://sql.js.org/dist/${file}`
      });

      // 尝试从localStorage加载现有数据库
      const savedDb = localStorage.getItem('sentence-alchemist-database');
      if (savedDb) {
        const uint8Array = new Uint8Array(JSON.parse(savedDb));
        this.db = new this.SQL.Database(uint8Array);
      } else {
        // 创建新数据库
        this.db = new this.SQL.Database();
      }

      // 创建表结构
      await this.createTables();
      
      // 创建索引
      await this.createIndexes();

      this.isInitialized = true;
      console.log('数据库初始化成功');
      
      return true;
    } catch (error) {
      console.error('数据库初始化失败:', error);
      return false;
    }
  }

  /**
   * 创建数据库表
   */
  async createTables() {
    for (const [tableName, schema] of Object.entries(DATABASE_SCHEMA)) {
      try {
        this.db.run(schema);
        console.log(`表 ${tableName} 创建成功`);
      } catch (error) {
        console.error(`创建表 ${tableName} 失败:`, error);
      }
    }
  }

  /**
   * 创建索引
   */
  async createIndexes() {
    for (const indexSql of DATABASE_INDEXES) {
      try {
        this.db.run(indexSql);
      } catch (error) {
        console.error('创建索引失败:', error);
      }
    }
  }

  /**
   * 保存数据库到localStorage
   */
  saveToStorage() {
    if (!this.db) return;
    
    try {
      const data = this.db.export();
      const dataArray = Array.from(data);
      localStorage.setItem('sentence-alchemist-database', JSON.stringify(dataArray));
    } catch (error) {
      console.error('保存数据库失败:', error);
    }
  }

  /**
   * 执行SQL查询
   * @param {string} sql - SQL语句
   * @param {Array} params - 参数
   * @returns {Array} 查询结果
   */
  query(sql, params = []) {
    if (!this.isInitialized) {
      throw new Error('数据库未初始化');
    }

    try {
      const stmt = this.db.prepare(sql);
      const results = [];
      
      while (stmt.step()) {
        const row = stmt.getAsObject();
        results.push(row);
      }
      
      stmt.free();
      return results;
    } catch (error) {
      console.error('查询执行失败:', error);
      throw error;
    }
  }

  /**
   * 执行SQL命令（INSERT, UPDATE, DELETE）
   * @param {string} sql - SQL语句
   * @param {Array} params - 参数
   * @returns {Object} 执行结果
   */
  run(sql, params = []) {
    if (!this.isInitialized) {
      throw new Error('数据库未初始化');
    }

    try {
      const result = this.db.run(sql, params);
      this.saveToStorage(); // 自动保存
      return result;
    } catch (error) {
      console.error('命令执行失败:', error);
      throw error;
    }
  }

  /**
   * 开始事务
   */
  beginTransaction() {
    this.run('BEGIN TRANSACTION');
  }

  /**
   * 提交事务
   */
  commit() {
    this.run('COMMIT');
    this.saveToStorage();
  }

  /**
   * 回滚事务
   */
  rollback() {
    this.run('ROLLBACK');
  }

  /**
   * 清空数据库
   */
  clear() {
    if (!this.isInitialized) return;
    
    const tables = ['sentences', 'idioms', 'words', 'learning_records', 'learning_sessions', 'user_preferences'];
    
    this.beginTransaction();
    try {
      for (const table of tables) {
        this.db.run(`DELETE FROM ${table}`);
      }
      this.commit();
      console.log('数据库已清空');
    } catch (error) {
      this.rollback();
      console.error('清空数据库失败:', error);
    }
  }

  /**
   * 获取数据库统计信息
   */
  getStats() {
    if (!this.isInitialized) return null;

    try {
      const stats = {};
      const tables = ['sentences', 'idioms', 'words', 'learning_records', 'learning_sessions'];
      
      for (const table of tables) {
        const result = this.query(`SELECT COUNT(*) as count FROM ${table}`);
        stats[table] = result[0]?.count || 0;
      }
      
      return stats;
    } catch (error) {
      console.error('获取统计信息失败:', error);
      return null;
    }
  }
}

// 导出单例实例
export const databaseManager = new DatabaseManager();
